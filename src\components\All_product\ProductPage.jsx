import React, { useState, useEffect, useContext } from "react";
import { useParams } from "react-router-dom";
import api from "../../Axios/axiosInstance";
import URL from "../../Axios/URL";
import { CartContext } from "../Context/CartContext";
import CartPanel from "../Navbar/CartPanel";

// Payment images
import gpay from "../../assets/image/payment/gpay.png";
import phonepe from "../../assets/image/payment/phonepe.png";
import paypal from "../../assets/image/payment/paypal.png";
import bhim from "../../assets/image/payment/bhim.png";

// Additional components
import FAQ from "./FAQ";
import Rating from "../Rating/ReviewSystem";
import PerfumeMarketingPage from "../PerfumeMarketingPage/PerfumeMarketingPage";

function ProductPage() {
  const { id } = useParams();
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [showOutOfStockPopup, setShowOutOfStockPopup] = useState(false);
  // Use CartContext
  const { cartItems, setCartItems } = useContext(CartContext);
  
  const paymentImages = [
    { name: "Google Pay", src: gpay },
    { name: "Phonepe", src: phonepe },
    { name: "PayPal", src: paypal },
    { name: "Bhim", src: bhim },
  ];

  // Function to render product labels based on API data
  const renderProductLabel = () => {
    if (!product) return null;

    if (product.new_arrival) {
      return (
        <span className="absolute top-3 left-3 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs px-3 py-1 rounded-full font-medium shadow-lg">
          New Arrival
        </span>
      );
    }

    if (product.is_trending) {
      return (
        <span className="absolute top-3 left-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs px-3 py-1 rounded-full font-medium shadow-lg">
          Trending
        </span>
      );
    }

    if (product.is_sale) {
      return (
        <span className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs px-3 py-1 rounded-full font-medium shadow-lg">
          Sale
        </span>
      );
    }

    return null;
  };

  // Fetch product by ID
  useEffect(() => {
    const fetchProductData = async () => {
      try {
        const response = await api.get(`get-product-by-id/`, {
          params: {
            product_id: id,
          },
        });
        setProduct(response.data.product);
        
        // Show popup if product is out of stock
        if (response.data.product && response.data.product.availability === false) {
          setShowOutOfStockPopup(true);
        }
      } catch (error) {
        console.error("Error fetching product data:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchProductData();
  }, [id]);

  // Add to cart function
  const addToCart = async () => {
    if (!product) return;
    
    // Check if product is out of stock
    if (product.availability === false) {
      setShowOutOfStockPopup(true);
      return;
    }
    
    // Determine the correct price fields based on what's available
    const price = product.product_price || 0;
    const originalPrice = product.product_old_price || price;
    const image = product.image ? 
      (Array.isArray(product.image) ? `${URL.PHOTO_URL}${product.image[0]}` : product.image) : 
      '/placeholder.svg';
    
    // Add the selected quantity to cart instead of just one item
    try {
      // Make multiple API calls based on quantity
      for (let i = 0; i < quantity; i++) {
        await api.post('add-to-cart/' + product.id);
      }
      
      // Update local cart state
      // Check if the item already exists in the cart
      const existingItemIndex = cartItems.findIndex(item => item.id === product.id);
      
      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        const updatedItems = [...cartItems];
        updatedItems[existingItemIndex].quantity += quantity;
        setCartItems(updatedItems);
      } else {
        // Add new item to cart
        setCartItems([...cartItems, {
          id: product.id,
          name: product.product_name,
          price: price,
          discountedPrice: price,
          originalPrice: originalPrice,
          image: image,
          variant: product.variant || 'Default',
          quantity: quantity
        }]);
      }
    } catch (error) {
      console.error('Error adding item to cart:', error);
      
      // Still update local cart state even if API fails
      const existingItemIndex = cartItems.findIndex(item => item.id === product.id);
      
      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        const updatedItems = [...cartItems];
        updatedItems[existingItemIndex].quantity += quantity;
        setCartItems(updatedItems);
      } else {
        // Add new item to cart
        setCartItems([...cartItems, {
          id: product.id,
          name: product.product_name,
          price: price,
          discountedPrice: price,
          originalPrice: originalPrice,
          image: image,
          variant: product.variant || 'Default',
          quantity: quantity
        }]);
      }
    }
    
    // Save to localStorage
    localStorage.setItem('cartItems', JSON.stringify(cartItems));
    
    // Open cart panel
    setIsCartOpen(true);
  };

  // Handle updating quantity in cart
  const handleUpdateQuantity = async (id, newQuantity) => {
    if (newQuantity < 1) return;
    
    // Update local state without API call
    const updatedItems = cartItems.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    );
    setCartItems(updatedItems);
    localStorage.setItem('cartItems', JSON.stringify(updatedItems));
    
    // Open cart panel to let it handle the API call
    setIsCartOpen(true);
  };

  // Handle removing item from cart
  const handleRemoveItem = async (id) => {
    // Update local state without API call
    const updatedItems = cartItems.filter(item => item.id !== id);
    setCartItems(updatedItems);
    localStorage.setItem('cartItems', JSON.stringify(updatedItems));
    
    // Open cart panel to let it handle the API call
    setIsCartOpen(true);
  };

  // Skeleton loading component
  const ProductSkeleton = () => {
    return (
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb skeleton */}
        <div className="mb-6 h-4 w-40 bg-gray-200 rounded animate-pulse"></div>
        
        {/* Product layout skeleton */}
        <div className="grid gap-8 md:grid-cols-2 mt-20">
          {/* Left: Image gallery skeleton */}
          <div className="space-y-4">
            <div className="relative aspect-square overflow-hidden rounded-xl border bg-gray-200 animate-pulse"></div>
            <div className="flex gap-4 overflow-x-auto pb-2">
              {[1, 2, 3].map((_, i) => (
                <div key={i} className="h-20 w-20 bg-gray-200 rounded-lg animate-pulse"></div>
              ))}
            </div>
          </div>
          
          {/* Right: Product info skeleton */}
          <div className="space-y-6">
            {/* Rating skeleton */}
            <div className="flex items-center gap-2 mb-2">
              <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
            </div>
            
            {/* Title skeleton */}
            <div className="h-8 w-3/4 bg-gray-200 rounded animate-pulse mb-4"></div>
            
            {/* Tags skeleton */}
            <div className="flex gap-2">
              {[1, 2, 3].map((_, i) => (
                <div key={i} className="h-6 w-16 bg-gray-200 rounded-full animate-pulse"></div>
              ))}
            </div>
            
            {/* Price skeleton */}
            <div className="space-y-2">
              <div className="flex items-baseline gap-2">
                <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-6 w-20 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
            </div>
            
            {/* Options skeleton */}
            <div className="space-y-4">
              <div className="h-6 w-32 bg-gray-200 rounded animate-pulse"></div>
              <div className="grid grid-cols-2 gap-4">
                {[1, 2].map((_, i) => (
                  <div key={i} className="rounded-lg border p-4 h-40 bg-gray-100">
                    <div className="mx-auto mb-2 h-24 w-24 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-3/4 mx-auto bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-4 w-1/2 mx-auto bg-gray-200 rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Buttons skeleton */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-10 flex-1 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="h-4 w-48 mx-auto bg-gray-200 rounded animate-pulse"></div>
            </div>
            
            {/* UPI section skeleton */}
            <div className="rounded-lg bg-gray-200 h-16 animate-pulse"></div>
            
            {/* Coupons skeleton */}
            <div className="space-y-2">
              <div className="h-6 w-40 bg-gray-200 rounded animate-pulse"></div>
              <div className="space-y-2">
                {[1, 2].map((_, i) => (
                  <div key={i} className="rounded-lg border h-16 bg-gray-100 animate-pulse"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Loading & not found states
  if (loading) {
    return <ProductSkeleton />;
  }

  if (!product) {
    return <div className="container mx-auto px-4 py-8">Product not found</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <div className="mb-6 text-sm text-gray-500">
        <span>Home</span> / <span>{product.product_name}</span>
      </div>

      {/* Product layout */}
      <div className="grid gap-8 md:grid-cols-2 mt-20">
        {/* Left: Image gallery */}
        <div className="space-y-4">
          <div className="relative aspect-square sm:h-[350px] sm:w-[610px] md:h-[350px] md:w-[350px] lg:h-[400px] lg:w-[480px]  xl:h-[470px] xl:w-[620px] 2xl:h-[470px] 2xl:w-[720px]  overflow-hidden rounded-xl border">
            <img
              src={`${URL.PHOTO_URL}${product.image[selectedImage]}`}
              alt={product.product_name}
              className="h-[100%] w-[100%] object-contain"
            />
            {/* Product Label */}
            {renderProductLabel()}
          </div>

          <div className="relative">
            <div className="flex gap-4 overflow-x-auto pb-2">
              {product.image.map((img, i) => (
                <button
                  key={i}
                  onClick={() => setSelectedImage(i)}
                  className={`relative min-w-[80px] overflow-hidden rounded-lg border-2 transition-all
                    ${selectedImage === i ? "border-[#B4945E]" : "border-transparent opacity-50 hover:opacity-75"}`}
                >
                  <img 
                    src={`${URL.PHOTO_URL}${img}`} 
                    className="h-20 w-20 object-contain" 
                    alt={`Product view ${i + 1}`}
                  />
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Right: Product info */}
        <div className="space-y-3">
          <div>
            <div className="mb-2 flex items-center gap-2">
              <div className="flex items-center text-yellow-500">
                <span>★</span>
                <span className="ml-1 text-sm font-medium">{product.average_rating}</span>
              </div>
              <span className="text-sm text-gray-500">({product.total_reviews} ratings)</span>
            </div>
            <h1 className="text-2xl font-semibold mb-2">{product.product_name}</h1>
            <div className="mb-0">
              {product.availability ? (
                <span className="text-green-600 font-medium">In Stock</span>
              ) : (
                <span className="text-red-600 font-medium">Out of Stock</span>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              {product.tags?.map((tag) => (
                <span key={tag} className="rounded-full bg-gray-200 px-3 py-1 text-xs font-medium">
                  {tag}
                </span>
              ))}
            </div>
          </div>

          {/* Price */}
          <div className="space-y-2">
            <div className="flex items-baseline gap-2">
              <span className="text-2xl font-bold">₹{product.product_price}</span>
              {product.product_old_price && (
                <>
                  <span className="text-gray-500 line-through">₹{product.product_old_price}</span>
                  <span className="text-sm font-medium text-green-600">
                    {Math.round((1 - product.product_price / product.product_old_price) * 100)}% OFF
                  </span>
                </>
              )}
            </div>
            <p className="text-sm text-gray-500">MRP (Inclusive Of All Taxes)</p>
          </div>

          {/* Options - Only show if product has options */}
          {product.options?.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-medium">Options</h3>
              <div className="grid grid-cols-2 gap-4">
                {product.options.map(option => (
                  <div key={option.name} className="cursor-pointer rounded-lg border p-4 shadow-sm hover:bg-gray-50">
                    <img 
                      src={option.image} 
                      alt={option.name} 
                      className="mx-auto mb-2 h-24 w-24 object-cover" 
                    />
                    <p className="text-center text-sm font-medium">{option.name}</p>
                    <p className="text-center text-sm">₹{option.price}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Quantity + Cart button */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center rounded-md border">
                <button
                  className="h-10 w-10 flex items-center justify-center text-gray-500 hover:bg-gray-100"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                >
                  −
                </button>
                <input 
                  type="number" 
                  value={quantity}
                  onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                  className="h-10 w-16 border-x text-center text-sm focus:outline-none [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                />
                <button 
                  className="h-10 w-10 flex items-center justify-center text-gray-500 hover:bg-gray-100"
                  onClick={() => setQuantity(quantity + 1)}
                >
                  +
                </button>
              </div>
              <button
                onClick={addToCart}
                className={`flex-1 flex items-center justify-center gap-2 rounded-md ${product.availability ? 'bg-[#B4945E] hover:bg-[#8B7355]' : 'bg-gray-400 cursor-not-allowed'} px-4 py-2.5 text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-[#B4945E] focus:ring-offset-2`}
              >
                <span>Add to Cart</span>
              </button>
            </div>
            <p className="text-center text-sm">✓ Delivery within 4-5 days</p>

            {/* UPI Discounts */}
            <div className="rounded-lg bg-[#2D1810] p-4 text-white">
              <div className="flex items-center justify-between">
                <p className="text-sm">Extra Discount on all UPI Payments</p>
                <div className="flex gap-2">
                  {paymentImages.map(payment => (
                    <img 
                      key={payment.name} 
                      src={payment.src} 
                      alt={payment.name} 
                      className="h-6 w-6 md:h-6 md:w-6  object-contain rounded-full bg-white p-1" 
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Coupons */}
            <div className="space-y-2">
              <h3 className="font-medium">Coupons & Offers</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">●</span>
                    <span>Buy 1 product & get 5% Off</span>
                  </div>
                  <span className="rounded-full border border-green-100 bg-green-50 px-3 py-1 text-xs">SAVED ₹</span>
                </div>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">●</span>
                    <span>Buy 2 products & get 10% Off</span>
                  </div>
                  <span className="rounded-full border border-green-100 bg-green-50 px-3 py-1 text-xs">SAVED ₹</span>
                </div>
              </div>
            </div>
          </div>  
        </div>
      </div>

      {/* Additional sections */}
      <PerfumeMarketingPage />
      <FAQ />
      <Rating id={id} />

      {/* Cart Panel */}
      <CartPanel 
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
        items={cartItems}
        onUpdateQuantity={handleUpdateQuantity}
        onRemoveItem={handleRemoveItem}
        onCartUpdate={setCartItems}
      />

      {/* Out of Stock Popup */}
      {showOutOfStockPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-red-600">Product Out of Stock</h3>
              <button 
                onClick={() => setShowOutOfStockPopup(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="mb-6">
              <p className="text-gray-700 mb-4">We're sorry, but {product?.product_name} is currently out of stock.</p>
              <p className="text-gray-600">Please check back later or browse our other products.</p>
            </div>
            <div className="flex justify-end">
              <button
                onClick={() => setShowOutOfStockPopup(false)}
                className="bg-[#B4945E] text-white px-4 py-2 rounded-md hover:bg-[#8B7355] transition-colors"
              >
                Continue Shopping
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ProductPage;
