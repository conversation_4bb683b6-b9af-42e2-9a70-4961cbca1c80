{"name": "my_web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/material": "^7.1.0", "axios": "^1.8.4", "cheerio": "^1.0.0", "context": "^3.0.33", "cors": "^2.8.5", "express": "^5.1.0", "framer-motion": "^12.4.3", "lucide-react": "^0.475.0", "my_web": "file:", "nodemailer": "^7.0.3", "puppeteer": "^24.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^7.1.5", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.2", "tailwindcss": "^3.4.17", "vite": "^6.1.0"}}